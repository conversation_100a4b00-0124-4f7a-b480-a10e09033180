{"name": "modern-portfolio", "private": false, "version": "0.0.1b", "homepage": "https://bilaldeadbeef.github.io/B-DEV/", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview --port 8080", "predeploy": "npm run build"}, "dependencies": {"@emailjs/browser": "^4.3.1", "dotenv": "^16.4.7", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.8.1", "react-type-animation": "^3.2.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@remix-run/dev": "^2.16.5", "@vitejs/plugin-react": "^1.3.2", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "gh-pages": "^6.3.0", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^6.1.0"}}