<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Generate OG Image</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #canvas {
            border: 1px solid #ccc;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <h1>Course Signup Open Graph Image Generator</h1>
    <p>Click the button below to generate and download the Open Graph image for the course signup page.</p>
    
    <canvas id="canvas" width="1200" height="630"></canvas>
    <br>
    <button onclick="generateImage()">Generate & Download Image</button>

    <script>
        function generateImage() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, 1200, 630);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Fill background
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 1200, 630);
            
            // Logo
            ctx.fillStyle = '#ff6b6b';
            ctx.font = 'bold 36px Arial';
            ctx.fillText('B| DEV', 60, 80);
            
            // Main title
            ctx.fillStyle = 'white';
            ctx.font = 'bold 56px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Course Registration', 600, 200);
            
            // Subtitle
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.font = '28px Arial';
            ctx.fillText('Join Our Premium Courses in Algeria', 600, 260);
            
            // Features
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.font = '22px Arial';
            ctx.fillText('✓ IT, Mathematics, Chemistry & More', 600, 340);
            ctx.fillText('✓ Flexible Pricing from 500 DZD/session', 600, 380);
            ctx.fillText('✓ Expert Instruction by Bilal Berkaoui', 600, 420);
            
            // Call to action button
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.roundRect(450, 480, 300, 60, 30);
            ctx.fill();
            ctx.stroke();
            
            // Button text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('Register Now', 600, 520);
            
            // Download the image
            const link = document.createElement('a');
            link.download = 'course-signup-og.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Polyfill for roundRect if not available
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
        
        // Generate image on page load
        window.onload = function() {
            generateImage();
        };
    </script>
</body>
</html>
