# Google Sheets Integration Setup

This guide will help you set up Google Sheets integration for the course registration form.

## Option 1: Google Apps Script (Recommended)

This is the easiest and most secure method.

### Step 1: Create a Google Sheet

1. Go to [Google Sheets](https://sheets.google.com)
2. Create a new spreadsheet
3. Name it "Course Registrations" or similar
4. Add the following headers in row 1:
   - A1: Timestamp
   - B1: First Name
   - C1: Last Name
   - D1: Wilaya
   - E1: Country
   - F1: Age
   - G1: University Grade
   - H1: Field of Studies
   - I1: Price Proposition (DZD)
   - J1: Motivation
   - K1: Agreed to Terms

### Step 2: Create Google Apps Script

1. In your Google Sheet, go to `Extensions` > `Apps Script`
2. Replace the default code with the following:

```javascript
function doPost(e) {
  try {
    // Get the active spreadsheet
    const sheet = SpreadsheetApp.getActiveSheet();
    
    // Parse the JSON data
    const data = JSON.parse(e.postData.contents);
    
    // Prepare the row data
    const rowData = [
      data.timestamp,
      data.firstName,
      data.lastName,
      data.wilaya,
      data.country,
      data.age,
      data.universityGrade,
      data.fieldOfStudies,
      data.priceProposition,
      data.motivation,
      data.agreeToTerms
    ];
    
    // Append the data to the sheet
    sheet.appendRow(rowData);
    
    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({success: true, message: 'Data saved successfully'}))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    // Return error response
    return ContentService
      .createTextOutput(JSON.stringify({success: false, error: error.toString()}))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet(e) {
  return ContentService
    .createTextOutput(JSON.stringify({message: 'Course Registration API is running'}))
    .setMimeType(ContentService.MimeType.JSON);
}
```

### Step 3: Deploy the Script

1. Click on `Deploy` > `New deployment`
2. Choose type: `Web app`
3. Description: "Course Registration API"
4. Execute as: `Me`
5. Who has access: `Anyone`
6. Click `Deploy`
7. Copy the Web app URL

### Step 4: Configure Environment Variables

1. Copy `.env.example` to `.env`
2. Add your Google Apps Script URL:
   ```
   VITE_GOOGLE_APPS_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
   ```

## Option 2: Direct Google Sheets API (Alternative)

This method requires more setup but gives you more control.

### Step 1: Enable Google Sheets API

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Sheets API
4. Create credentials (API Key)

### Step 2: Configure Sheet Permissions

1. Share your Google Sheet with "Anyone with the link can edit"
2. Or create a service account and share with the service account email

### Step 3: Configure Environment Variables

```
VITE_GOOGLE_SHEET_ID=your_google_sheet_id
VITE_GOOGLE_SHEETS_API_KEY=your_api_key
```

## Fallback Options

If Google Sheets integration fails, the system will:

1. Store data locally in browser's localStorage
2. Automatically download a CSV file
3. You can manually upload the CSV to Google Sheets or Excel

## Testing

1. Fill out the course registration form
2. Check your Google Sheet for new entries
3. If it fails, check the browser console for errors
4. Verify that a CSV file was downloaded as backup

## Security Notes

- Never commit your `.env` file to version control
- The Google Apps Script method is more secure as it doesn't expose API keys
- Consider implementing rate limiting for production use
- Validate and sanitize data on the server side (Google Apps Script)

## Troubleshooting

### Common Issues:

1. **CORS Errors**: Use Google Apps Script method instead of direct API
2. **Permission Denied**: Check sheet sharing settings
3. **Script Not Found**: Verify the Apps Script URL is correct
4. **Data Not Appearing**: Check if the script is deployed and accessible

### Debug Steps:

1. Test the Google Apps Script URL directly in browser
2. Check browser console for error messages
3. Verify environment variables are loaded correctly
4. Test with a simple curl command:

```bash
curl -X POST "YOUR_APPS_SCRIPT_URL" \
  -H "Content-Type: application/json" \
  -d '{"timestamp":"2024-01-01","firstName":"Test","lastName":"User"}'
```
