import { Helmet } from 'react-helmet-async';
import CourseNotification from '../components/CourseNotification';
import Hero from '../components/Hero';
import Projects from '../components/Projects';
import Skills from '../components/Skills';
import Contact from '../components/Contact';

const Home = () => {
  return (
    <>
      <Helmet>
        <title>B DEV - Bilal Berkaoui | Full Stack Developer</title>
        <meta name="description" content="Portfolio of Bilal Berkaoui - a full-stack web developer building modern websites and applications using React, Node.js, and more" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://berkaouibilal.netlify.app/" />
        <meta property="og:title" content="B DEV - Bilal Berkaoui | Full Stack Developer" />
        <meta property="og:description" content="Portfolio of Bilal Berkaoui - a full-stack web developer building modern websites and applications using React, Node.js, and more" />
        <meta property="og:image" content="/opengraph/og-image.jpg" />
        <meta property="og:site_name" content="B DEV - Bilal Berkaoui" />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:title" content="B DEV - Bilal Berkaoui | Full Stack Developer" />
        <meta property="twitter:description" content="Portfolio of Bilal Berkaoui - a full-stack web developer building modern websites and applications using React, Node.js, and more" />
        <meta property="twitter:image" content="/opengraph/og-image.jpg" />

        <link rel="canonical" href="https://berkaouibilal.netlify.app/" />
      </Helmet>

      <CourseNotification />
      <Hero />
      <Projects />
      <Skills />
      <Contact />
    </>
  );
};

export default Home;
