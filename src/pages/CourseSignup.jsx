import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, User, MapPin, Calendar, GraduationCap, BookOpen, DollarSign, MessageSquare, FileText } from 'lucide-react';
import toast from 'react-hot-toast';
import { submitToGoogleSheets, exportAsCSV, storeLocally } from '../services/googleSheets';

const CourseSignup = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showTerms, setShowTerms] = useState(false);
  const [formData, setFormData] = useState({
    // Step 1 - Basic Information
    firstName: '',
    lastName: '',
    wilaya: '',
    country: 'Algeria',
    age: '',
    universityGrade: '',
    
    // Step 2 - Additional Information
    fieldOfStudies: '',
    priceProposition: 500,
    motivation: '',
    agreeToTerms: false
  });

  const wilayas = [
    'Adrar', 'Chlef', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>rasset', '<PERSON><PERSON><PERSON><PERSON>', 'Tlemcen', 'Tiaret', 'Tizi Ouzou', 'Alger',
    'Djelfa', 'Jijel', 'Sétif', 'Saïda', 'Skikda', 'Sidi Bel Abbès', 'Annaba', 'Guelma',
    '<PERSON>', 'Médéa', 'Mostaganem', 'MSila', 'Mascara', 'Ouargla', 'Oran', 'El <PERSON>adh',
    'Illizi', 'Bordj Bou Arréridj', 'Boumerdès', 'El Tarf', 'Tindouf', 'Tissemsilt', 'El Oued',
    'Khenchela', 'Souk Ahras', 'Tipaza', 'Mila', 'Aïn Defla', 'Naâma', 'Aïn Témouchent',
    'Ghardaïa', 'Relizane'
  ];

  const universityGrades = ['L1', 'L2', 'L3'];
  
  const fieldsOfStudy = [
    'IT/Computer Science', 'Mathematics', 'Chemistry', 'Physics', 'Biology', 
    'Engineering', 'Medicine', 'Economics', 'Business', 'Literature', 'Other'
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleNext = () => {
    if (validateStep1()) {
      setCurrentStep(2);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(1);
  };

  const validateStep1 = () => {
    const { firstName, lastName, wilaya, age, universityGrade } = formData;
    if (!firstName || !lastName || !wilaya || !age || !universityGrade) {
      toast.error('Please fill in all required fields');
      return false;
    }
    if (age < 16 || age > 100) {
      toast.error('Please enter a valid age');
      return false;
    }
    return true;
  };

  const validateStep2 = () => {
    const { fieldOfStudies, motivation, agreeToTerms } = formData;
    if (!fieldOfStudies || !motivation.trim()) {
      toast.error('Please fill in all required fields');
      return false;
    }
    if (motivation.split(' ').length > 15) {
      toast.error('Motivation should be maximum 15 words');
      return false;
    }
    if (!agreeToTerms) {
      toast.error('Please agree to the terms and conditions');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateStep2()) return;

    try {
      // Store data locally as backup
      storeLocally(formData);

      // Try to submit to Google Sheets if configured
      try {
        await submitToGoogleSheets(formData);
        toast.success('Registration submitted successfully to Google Sheets!');
      } catch (sheetsError) {
        console.warn('Google Sheets submission failed, data stored locally:', sheetsError);
        // Also export as CSV as fallback
        exportAsCSV(formData);
        toast.success('Registration saved! CSV file downloaded for manual upload.');
      }

      // Reset form
      setFormData({
        firstName: '', lastName: '', wilaya: '', country: 'Algeria', age: '',
        universityGrade: '', fieldOfStudies: '', priceProposition: 500,
        motivation: '', agreeToTerms: false
      });
      setCurrentStep(1);
    } catch (error) {
      console.error('Submission error:', error);
      toast.error('Failed to submit registration. Please try again.');
    }
  };

  return (
    <div className="min-h-screen pt-20 pb-12 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-2xl mx-auto"
        >
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Course Registration
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Join our paid courses and enhance your skills
            </p>
          </div>

          {/* Progress Indicator */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center space-x-4">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                currentStep >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                1
              </div>
              <div className={`h-1 w-16 ${
                currentStep >= 2 ? 'bg-purple-600' : 'bg-gray-200'
              }`} />
              <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                currentStep >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                2
              </div>
            </div>
          </div>

          {/* Form Card */}
          <motion.div
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8"
            layout
          >
            <form onSubmit={handleSubmit}>
              <AnimatePresence mode="wait">
                {currentStep === 1 && (
                  <motion.div
                    key="step1"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                  >
                    <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                      <User className="w-6 h-6 mr-2 text-purple-600" />
                      Basic Information
                    </h2>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      {/* First Name */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          First Name *
                        </label>
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Enter your first name"
                          required
                        />
                      </div>

                      {/* Last Name */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Last Name *
                        </label>
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Enter your last name"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6">
                      {/* Wilaya */}
                      <div>
                        <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          <MapPin className="w-4 h-4 mr-1" />
                          Wilaya *
                        </label>
                        <select
                          name="wilaya"
                          value={formData.wilaya}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          required
                        >
                          <option value="">Select your wilaya</option>
                          {wilayas.map(wilaya => (
                            <option key={wilaya} value={wilaya}>{wilaya}</option>
                          ))}
                        </select>
                      </div>

                      {/* Country */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Country
                        </label>
                        <input
                          type="text"
                          name="country"
                          value={formData.country}
                          readOnly
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white cursor-not-allowed"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6">
                      {/* Age */}
                      <div>
                        <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          <Calendar className="w-4 h-4 mr-1" />
                          Age *
                        </label>
                        <input
                          type="number"
                          name="age"
                          value={formData.age}
                          onChange={handleInputChange}
                          min="16"
                          max="100"
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Enter your age"
                          required
                        />
                      </div>

                      {/* University Grade */}
                      <div>
                        <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          <GraduationCap className="w-4 h-4 mr-1" />
                          University Grade *
                        </label>
                        <select
                          name="universityGrade"
                          value={formData.universityGrade}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          required
                        >
                          <option value="">Select your grade</option>
                          {universityGrades.map(grade => (
                            <option key={grade} value={grade}>{grade}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="flex justify-end mt-8">
                      <motion.button
                        type="button"
                        onClick={handleNext}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                      >
                        Next Step
                        <ChevronRight className="w-4 h-4 ml-2" />
                      </motion.button>
                    </div>
                  </motion.div>
                )}

                {currentStep === 2 && (
                  <motion.div
                    key="step2"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                  >
                    <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                      <BookOpen className="w-6 h-6 mr-2 text-purple-600" />
                      Additional Information
                    </h2>

                    {/* Field of Studies */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Field of Studies *
                      </label>
                      <select
                        name="fieldOfStudies"
                        value={formData.fieldOfStudies}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      >
                        <option value="">Select your field of studies</option>
                        {fieldsOfStudy.map(field => (
                          <option key={field} value={field}>{field}</option>
                        ))}
                      </select>
                    </div>

                    {/* Price Proposition */}
                    <div className="mb-6">
                      <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <DollarSign className="w-4 h-4 mr-1" />
                        Price Proposition per Session (DZD) *
                      </label>
                      <div className="space-y-3">
                        <input
                          type="range"
                          name="priceProposition"
                          min="500"
                          max="5000"
                          step="100"
                          value={formData.priceProposition}
                          onChange={handleInputChange}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
                        />
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span>500 DZD</span>
                          <span className="font-semibold text-purple-600 dark:text-purple-400">
                            {formData.priceProposition} DZD
                          </span>
                          <span>5000 DZD</span>
                        </div>
                      </div>
                    </div>

                    {/* Motivation */}
                    <div className="mb-6">
                      <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <MessageSquare className="w-4 h-4 mr-1" />
                        Why do you want to sign up? (Max 15 words) *
                      </label>
                      <textarea
                        name="motivation"
                        value={formData.motivation}
                        onChange={handleInputChange}
                        rows="3"
                        maxLength="150"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
                        placeholder="Tell us your motivation in maximum 15 words..."
                        required
                      />
                      <div className="text-right text-sm text-gray-500 mt-1">
                        {formData.motivation.split(' ').filter(word => word.length > 0).length}/15 words
                      </div>
                    </div>

                    {/* Terms Agreement */}
                    <div className="mb-8">
                      <div className="flex items-start space-x-3">
                        <input
                          type="checkbox"
                          name="agreeToTerms"
                          checked={formData.agreeToTerms}
                          onChange={handleInputChange}
                          className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          required
                        />
                        <label className="text-sm text-gray-700 dark:text-gray-300">
                          I agree to the{' '}
                          <button
                            type="button"
                            onClick={() => setShowTerms(true)}
                            className="text-purple-600 hover:text-purple-700 underline focus:outline-none"
                          >
                            Terms and Conditions
                          </button>
                          {' '}*
                        </label>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <motion.button
                        type="button"
                        onClick={handlePrevious}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                      >
                        <ChevronLeft className="w-4 h-4 mr-2" />
                        Previous
                      </motion.button>

                      <motion.button
                        type="submit"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                      >
                        <FileText className="w-4 h-4 mr-2" />
                        Submit Registration
                      </motion.button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </form>
          </motion.div>
        </motion.div>

        {/* Terms and Conditions Modal */}
        <AnimatePresence>
          {showTerms && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
              onClick={() => setShowTerms(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Terms and Conditions
                  </h3>
                  <div className="prose dark:prose-invert max-w-none text-sm">
                    <h4 className="text-lg font-semibold mb-2">1. Course Registration and Payment</h4>
                    <p className="mb-4">
                      By registering for our paid courses, you agree to pay the proposed session fee as indicated during registration.
                      Payment must be made in Algerian Dinars (DZD) and is due before each session unless otherwise arranged.
                    </p>

                    <h4 className="text-lg font-semibold mb-2">2. Course Content and Schedule</h4>
                    <p className="mb-4">
                      Course content, schedule, and duration will be communicated after registration confirmation.
                      We reserve the right to modify course content to ensure the best learning experience.
                    </p>

                    <h4 className="text-lg font-semibold mb-2">3. Attendance and Participation</h4>
                    <p className="mb-4">
                      Regular attendance is expected. If you miss a session, makeup arrangements may be available
                      subject to instructor availability and additional fees may apply.
                    </p>

                    <h4 className="text-lg font-semibold mb-2">4. Cancellation Policy</h4>
                    <p className="mb-4">
                      Course cancellations must be made at least 24 hours in advance.
                      Refunds are subject to our discretion and may incur administrative fees.
                    </p>

                    <h4 className="text-lg font-semibold mb-2">5. Intellectual Property</h4>
                    <p className="mb-4">
                      All course materials, including but not limited to presentations, documents, and recordings,
                      are the intellectual property of the instructor and may not be shared or distributed without permission.
                    </p>

                    <h4 className="text-lg font-semibold mb-2">6. Privacy and Data Protection</h4>
                    <p className="mb-4">
                      Your personal information will be used solely for course administration and communication.
                      We comply with applicable data protection laws in Algeria.
                    </p>

                    <h4 className="text-lg font-semibold mb-2">7. Limitation of Liability</h4>
                    <p className="mb-4">
                      We strive to provide quality education but cannot guarantee specific outcomes.
                      Our liability is limited to the fees paid for the course.
                    </p>

                    <h4 className="text-lg font-semibold mb-2">8. Governing Law</h4>
                    <p className="mb-4">
                      These terms are governed by the laws of Algeria. Any disputes will be resolved
                      through the appropriate legal channels in Algeria.
                    </p>
                  </div>

                  <div className="flex justify-end mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <motion.button
                      onClick={() => setShowTerms(false)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="px-6 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                    >
                      I Understand
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default CourseSignup;
