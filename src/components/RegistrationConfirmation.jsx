import { motion } from 'framer-motion';
import { CheckCircle, Download, FileText, User, BookOpen, DollarSign } from './Icons';
import { generateRegistrationReceipt, generateTermsAndConditionsPDF } from '../services/pdfGenerator';

const RegistrationConfirmation = ({ formData, onClose }) => {
  const handleDownloadReceipt = () => {
    try {
      const result = generateRegistrationReceipt(formData);
      if (result.success) {
        // You could also show a success message here
        console.log('Receipt generated:', result.fileName);
      }
    } catch (error) {
      console.error('Error generating receipt:', error);
    }
  };

  const handleDownloadTerms = () => {
    try {
      generateTermsAndConditionsPDF();
    } catch (error) {
      console.error('Error generating terms PDF:', error);
    }
  };

  const registrationId = `REG-${Date.now().toString().slice(-8)}`;
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6 rounded-t-xl">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-8 h-8" />
            <div>
              <h2 className="text-2xl font-bold">Registration Successful!</h2>
              <p className="text-green-100">Your course registration has been confirmed</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Registration Details */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Registration Details</h3>
              <span className="text-sm text-gray-500 dark:text-gray-400">ID: {registrationId}</span>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              <p><strong>Date:</strong> {currentDate}</p>
              <p><strong>Status:</strong> <span className="text-green-600 font-semibold">Confirmed</span></p>
            </div>
          </div>

          {/* Student Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <User className="w-5 h-5 text-purple-600" />
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Student Information</h4>
                  <p className="text-gray-600 dark:text-gray-300">{formData.firstName} {formData.lastName}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {formData.age} years old • {formData.wilaya}, {formData.country}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Grade: {formData.universityGrade} • {formData.profession}
                  </p>
                  {formData.profession === 'student' && formData.university && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">University: {formData.university}</p>
                  )}
                  {formData.profession === 'professional' && formData.jobTitle && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">Job: {formData.jobTitle}</p>
                  )}
                  {formData.profession === 'other' && formData.otherProfession && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">Profession: {formData.otherProfession}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <BookOpen className="w-5 h-5 text-purple-600" />
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Course Details</h4>
                  <p className="text-gray-600 dark:text-gray-300">{formData.fieldOfStudies}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <DollarSign className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {formData.priceProposition} DZD per session
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Motivation */}
          <div className="mb-6">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Motivation</h4>
            <p className="text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              "{formData.motivation}"
            </p>
          </div>

          {/* Download Actions */}
          <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Download Documents</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <motion.button
                onClick={handleDownloadReceipt}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center justify-center space-x-2 bg-purple-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Download Receipt</span>
              </motion.button>

              <motion.button
                onClick={handleDownloadTerms}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center justify-center space-x-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <FileText className="w-4 h-4" />
                <span>Download Terms</span>
              </motion.button>
            </div>
          </div>

          {/* Next Steps */}
          <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">What's Next?</h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• You will receive a confirmation email shortly</li>
              <li>• Course schedule and payment details will be sent within 24 hours</li>
              <li>• Keep your registration receipt for your records</li>
              <li>• Contact <NAME_EMAIL> for any questions</li>
            </ul>
          </div>

          {/* Close Button */}
          <div className="flex justify-end mt-6">
            <motion.button
              onClick={onClose}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors"
            >
              Close
            </motion.button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default RegistrationConfirmation;
