import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Moon, Sun, FileDown, Menu, X, User, FolderOpen, Code, Mail, ChevronDown } from 'lucide-react';
import { useTheme } from '../context/ThemeContext';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isResumeDropdownOpen, setIsResumeDropdownOpen] = useState(false);
  const [isMobileResumeOpen, setIsMobileResumeOpen] = useState(false);
  const { isDarkMode, toggleTheme } = useTheme();

  // Navigation items with icons
  const navItems = [
    { name: 'About', icon: User, id: 'about' },
    { name: 'Projects', icon: FolderOpen, id: 'projects' },
    { name: 'Skills', icon: Code, id: 'skills' },
    { name: 'Contact', icon: Mail, id: 'contact' },
  ];

  // Resume links
  const resumeLinks = [
    {
      name: 'English Resume',
      url: 'https://drive.google.com/file/d/1NQcxXTgQuzf0i833esGDkTF7haYWpI3z/view?usp=sharing'
    },
    {
      name: 'French Resume',
      url: 'https://drive.google.com/file/d/1ZIMlebPoa9d155OIEJONmNthooER8sUy/view?usp=sharing' // Replace with actual French resume link
    }
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollTo = (id) => {
    const element = document.getElementById(id);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleMobileNavClick = (id) => {
    scrollTo(id);
    setIsMobileMenuOpen(false);
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/80 dark:bg-gray-900/80 backdrop-blur-md shadow-lg'
          : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <motion.div
            className="flex-shrink-0 cursor-pointer"
            whileHover={{ scale: 1.05 }}
            onClick={() => scrollTo('hero')}
          >
            <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              B| DEV
            </span>
          </motion.div>

          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {navItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <motion.button
                    key={item.name}
                    whileHover={{ y: -2 }}
                    className="flex items-center text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 px-3 py-2 rounded-md text-sm font-medium"
                    onClick={() => scrollTo(item.id)}
                  >
                    <IconComponent className="w-4 h-4 mr-1" />
                    {item.name}
                  </motion.button>
                );
              })}

              {/* Resume Dropdown */}
              <div
                className="relative"
                onMouseEnter={() => setIsResumeDropdownOpen(true)}
                onMouseLeave={() => setIsResumeDropdownOpen(false)}
              >
                <motion.button
                  whileHover={{ y: -2 }}
                  className="flex items-center text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 px-3 py-2 rounded-md text-sm font-medium"
                >
                  <FileDown className="w-4 h-4 mr-1" />
                  Resume
                  <ChevronDown className="w-3 h-3 ml-1" />
                </motion.button>

                <AnimatePresence>
                  {isResumeDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50"
                    >
                      {resumeLinks.map((link) => (
                        <motion.a
                          key={link.name}
                          href={link.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          whileHover={{ backgroundColor: 'rgba(147, 51, 234, 0.1)' }}
                          className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                        >
                          {link.name}
                        </motion.a>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={toggleTheme}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              {isDarkMode ? (
                <Sun className="w-5 h-5 text-yellow-500" />
              ) : (
                <Moon className="w-5 h-5 text-gray-700" />
              )}
            </motion.button>

            {/* Mobile menu button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5 text-gray-700 dark:text-gray-300" />
              ) : (
                <Menu className="w-5 h-5 text-gray-700 dark:text-gray-300" />
              )}
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="md:hidden bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-t border-gray-200 dark:border-gray-700 transition-colors duration-300"
            >
              <div className="px-4 py-4 space-y-2">
                {navItems.map((item) => {
                  const IconComponent = item.icon;
                  return (
                    <motion.button
                      key={item.name}
                      whileHover={{ x: 5 }}
                      className="flex items-center w-full text-left text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 px-3 py-2 rounded-md text-base font-medium"
                      onClick={() => handleMobileNavClick(item.id)}
                    >
                      <IconComponent className="w-4 h-4 mr-2" />
                      {item.name}
                    </motion.button>
                  );
                })}

                {/* Mobile Resume Section */}
                <div>
                  <motion.button
                    whileHover={{ x: 5 }}
                    className="flex items-center justify-between w-full text-left text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 px-3 py-2 rounded-md text-base font-medium"
                    onClick={() => setIsMobileResumeOpen(!isMobileResumeOpen)}
                  >
                    <div className="flex items-center">
                      <FileDown className="w-4 h-4 mr-2" />
                      Resume
                    </div>
                    <motion.div
                      animate={{ rotate: isMobileResumeOpen ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </motion.div>
                  </motion.button>

                  <AnimatePresence>
                    {isMobileResumeOpen && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="ml-6 mt-1 space-y-1"
                      >
                        {resumeLinks.map((link) => (
                          <motion.a
                            key={link.name}
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            whileHover={{ x: 5 }}
                            className="block text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 px-3 py-2 rounded-md text-sm"
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            {link.name}
                          </motion.a>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
  );
};

export default Navbar;
