import React from 'react';
import { motion } from 'framer-motion';
import { Github, ExternalLink } from 'lucide-react';
import { useInView } from 'react-intersection-observer';

const projects = [
  {
    title: 'XO Game',
    description: 'A simple tic-tac-toe game built with pure HTML, CSS, and JavaScript.',
    image: 'https://i.postimg.cc/TwT1w8KC/Screenshot-2025-05-02-at-12-26-19-X-O-Game.png',
    techStack: ['HTML', 'CSS', 'JavaScript'],
    liveUrl: 'https://cautious-pelican.static.domains/',
    githubUrl: 'https://github.com/berkaouibilal',
    isPrivateRepo: false,
  },
  {
    title: 'Pepsi Rebranded Website',
    description: 'A rebranded website for Pepsi, showcasing modern design and responsiveness.',
    image: 'https://i.postimg.cc/rwpvqfyx/Screenshot-2025-05-02-at-12-43-20-PEPSI-REBRANDED.png',
    techStack: ['React', 'Tailwind CSS', 'JavaScript', 'Framer Motion', 'React Router', 'React Icons'],
    liveUrl: 'https://pepsi-rebranded.vercel.app/',
    githubUrl: 'https://github.com/berkaouibilal/pepsi-rebranded',
  },
  {
    title: 'Hotel Management System',
    description: 'A web application for managing hotel operations, including room bookings, guest information, and more.',
    image: 'https://i.ibb.co/Tq4tNPBK/Screenshot-2025-05-30-at-21-43-48-B-Hotel.png',
    techStack: ['React', 'Tailwind CSS', 'JavaScript', 'Framer Motion', 'React Router', 'React Icons', 'Firebase', 'lucide-react'],
    liveUrl: 'https://hms-berkaouibilal.netlify.app/',
    githubUrl: '',
    isPrivateRepo: true,
  },
  {
    title: 'WatanEvents',
    description: 'A web application for managing events, including event creation, registration, and more. Specifically for the Watan Events team.',
    image: 'https://i.ibb.co/S4ZfVpC4/Screenshot-2025-05-30-at-21-47-44-Watan-Events.png',
    techStack: ['React', 'Tailwind CSS', 'JavaScript', 'Framer Motion', 'React Router', 'React Icons', 'Supabase', 'lucide-react'],
    liveUrl: 'https://watanevents-berkaouibilal.netlify.app/',
    githubUrl: '',
    isPrivateRepo: true,
  },
  {
    title: 'Pizzeria Website (International Colaboration)',
    description: 'A website for a pizzeria, with features like product listings, shopping cart, and checkout.',
    image: 'https://i.ibb.co/0jWP9TJP/Screenshot-2025-05-30-at-21-50-04-B-ZZA-Hub-Pizzeria-Artisanale.png',
    techStack: ['React', 'Tailwind CSS', 'JavaScript', 'Framer Motion', 'React Router', 'React Icons', 'lucide-react', 'Firebase'],
    liveUrl: 'https://bzza-pizzas-berkaoui.netlify.app/',
    githubUrl: '',
    isPrivateRepo: true,
  },


];

const Projects = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section id="projects" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-3xl sm:text-4xl font-bold text-center mb-12 text-gray-900 dark:text-white"
        >
          Featured Projects
        </motion.h2>

        <div ref={ref} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              className="bg-white dark:bg-gray-900 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover transform hover:scale-110 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
                  {project.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {project.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.techStack.map((tech, i) => (
                    <span
                      key={i}
                      className="px-3 py-1 text-sm bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
                <div className="flex space-x-4">
                  {project.githubUrl && (
                    <a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400"
                    >
                      <Github className="w-5 h-5 mr-2" />
                      GitHub
                    </a>
                  )}
                  <a
                    href={project.liveUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400"
                  >
                    <ExternalLink className="w-5 h-5 mr-2" />
                    Live Demo
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;