import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { BookOpen, X, GraduationCap, DollarSign, Users } from '../components/Icons';

const CourseNotification = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -100 }}
        transition={{ duration: 0.5 }}
        className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg"
      >
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <BookOpen className="w-6 h-6" />
                <span className="font-bold text-lg">New Course Available!</span>
              </div>
              
              <div className="hidden md:flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-1">
                  <GraduationCap className="w-4 h-4" />
                  <span>IT, Math, Chemistry & More</span>
                </div>
                <div className="flex items-center space-x-1">
                  <DollarSign className="w-4 h-4" />
                  <span>From 500 DZD/session</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="w-4 h-4" />
                  <span>Expert Instruction</span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Link
                to="/course-signup"
                className="bg-white text-purple-600 px-4 py-2 rounded-full font-semibold text-sm hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1"
              >
                <BookOpen className="w-4 h-4" />
                <span>Register Now</span>
              </Link>
              
              <button
                onClick={() => setIsVisible(false)}
                className="p-1 hover:bg-white/20 rounded-full transition-colors duration-200"
                aria-label="Close notification"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
          
          {/* Mobile version */}
          <div className="md:hidden mt-2 text-sm">
            <div className="flex items-center justify-between">
              <span>Premium courses in Algeria - IT, Math, Chemistry & More</span>
              <span className="font-semibold">From 500 DZD/session</span>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default CourseNotification;
