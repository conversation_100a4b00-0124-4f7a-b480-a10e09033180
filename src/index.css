@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply antialiased text-gray-900 dark:text-white;
  }
}

@layer components {
  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24;
  }

  .section-title {
    @apply text-3xl sm:text-4xl font-bold text-center mb-12;
  }

  /* Custom range slider styles */
  .slider {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }

  .slider::-webkit-slider-track {
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
  }

  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #9333ea;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .slider::-webkit-slider-thumb:hover {
    background: #7c3aed;
    transform: scale(1.1);
  }

  .slider::-moz-range-track {
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
    border: none;
  }

  .slider::-moz-range-thumb {
    background: #9333ea;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
  }

  .slider::-moz-range-thumb:hover {
    background: #7c3aed;
    transform: scale(1.1);
  }

  /* Dark mode slider styles */
  .dark .slider::-webkit-slider-track {
    background: #374151;
  }

  .dark .slider::-moz-range-track {
    background: #374151;
  }
}