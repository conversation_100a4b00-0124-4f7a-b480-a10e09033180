import jsPDF from 'jspdf';

export const generateRegistrationReceipt = (formData) => {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  
  // Colors
  const primaryColor = [102, 126, 234]; // Purple
  const secondaryColor = [118, 75, 162]; // Dark purple
  const textColor = [51, 51, 51]; // Dark gray
  const lightGray = [128, 128, 128];
  
  // Header with gradient effect
  doc.setFillColor(...primaryColor);
  doc.rect(0, 0, pageWidth, 40, 'F');
  
  // Logo and title
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(24);
  doc.setFont('helvetica', 'bold');
  doc.text('B| DEV', 20, 25);
  
  doc.setFontSize(16);
  doc.setFont('helvetica', 'normal');
  doc.text('Course Registration Receipt', pageWidth - 20, 25, { align: 'right' });
  
  // Receipt details
  doc.setTextColor(...textColor);
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('REGISTRATION CONFIRMATION', 20, 60);
  
  // Registration ID and date
  const registrationId = `REG-${Date.now().toString().slice(-8)}`;
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.setTextColor(...lightGray);
  doc.text(`Registration ID: ${registrationId}`, 20, 70);
  doc.text(`Date: ${currentDate}`, pageWidth - 20, 70, { align: 'right' });
  
  // Student Information Section
  let yPos = 90;
  doc.setTextColor(...primaryColor);
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('STUDENT INFORMATION', 20, yPos);
  
  yPos += 15;
  doc.setTextColor(...textColor);
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  
  const studentInfo = [
    ['Full Name:', `${formData.firstName} ${formData.lastName}`],
    ['Age:', `${formData.age} years old`],
    ['Location:', `${formData.wilaya}, ${formData.country}`],
    ['University Grade:', formData.universityGrade],
    ['Profession:', formData.profession.charAt(0).toUpperCase() + formData.profession.slice(1)]
  ];
  
  // Add profession-specific details
  if (formData.profession === 'student' && formData.university) {
    studentInfo.push(['University:', formData.university]);
  } else if (formData.profession === 'professional' && formData.jobTitle) {
    studentInfo.push(['Job Title:', formData.jobTitle]);
  } else if (formData.profession === 'other' && formData.otherProfession) {
    studentInfo.push(['Profession Details:', formData.otherProfession]);
  }
  
  studentInfo.forEach(([label, value]) => {
    doc.setFont('helvetica', 'bold');
    doc.text(label, 20, yPos);
    doc.setFont('helvetica', 'normal');
    doc.text(value, 80, yPos);
    yPos += 12;
  });
  
  // Course Information Section
  yPos += 10;
  doc.setTextColor(...primaryColor);
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('COURSE INFORMATION', 20, yPos);
  
  yPos += 15;
  doc.setTextColor(...textColor);
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  
  const courseInfo = [
    ['Field of Studies:', formData.fieldOfStudies],
    ['Proposed Price:', `${formData.priceProposition} DZD per session`],
    ['Motivation:', formData.motivation]
  ];
  
  courseInfo.forEach(([label, value]) => {
    doc.setFont('helvetica', 'bold');
    doc.text(label, 20, yPos);
    doc.setFont('helvetica', 'normal');
    
    // Handle long text wrapping for motivation
    if (label === 'Motivation:') {
      const splitText = doc.splitTextToSize(value, pageWidth - 100);
      doc.text(splitText, 80, yPos);
      yPos += splitText.length * 6;
    } else {
      doc.text(value, 80, yPos);
      yPos += 12;
    }
  });
  
  // Terms Agreement Section
  yPos += 10;
  doc.setTextColor(...primaryColor);
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('TERMS AGREEMENT', 20, yPos);
  
  yPos += 15;
  doc.setTextColor(...textColor);
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.text('✓ I agree to the Terms and Conditions', 20, yPos);
  
  yPos += 15;
  doc.setFontSize(10);
  doc.setTextColor(...lightGray);
  doc.text('By registering, you acknowledge that you have read and agree to our terms and conditions.', 20, yPos);
  
  // Footer
  const footerY = pageHeight - 30;
  doc.setFillColor(...lightGray);
  doc.rect(0, footerY - 5, pageWidth, 1, 'F');
  
  doc.setTextColor(...lightGray);
  doc.setFontSize(9);
  doc.text('B DEV - Premium Course Registration System', 20, footerY + 5);
  doc.text('Contact: <EMAIL>', pageWidth - 20, footerY + 5, { align: 'right' });
  
  // Save the PDF
  const fileName = `Course_Registration_${formData.firstName}_${formData.lastName}_${registrationId}.pdf`;
  doc.save(fileName);
  
  return {
    success: true,
    fileName,
    registrationId
  };
};

export const generateTermsAndConditionsPDF = () => {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  
  // Header
  doc.setFillColor(102, 126, 234);
  doc.rect(0, 0, pageWidth, 30, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('Terms and Conditions', pageWidth / 2, 20, { align: 'center' });
  
  // Content
  let yPos = 50;
  doc.setTextColor(51, 51, 51);
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('B DEV Course Registration Terms', 20, yPos);
  
  yPos += 20;
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  
  const terms = [
    {
      title: '1. Course Registration and Payment',
      content: 'By registering for our paid courses, you agree to pay the proposed session fee as indicated during registration. Payment must be made in Algerian Dinars (DZD) and is due before each session unless otherwise arranged.'
    },
    {
      title: '2. Course Content and Schedule',
      content: 'Course content, schedule, and duration will be communicated after registration confirmation. We reserve the right to modify course content to ensure the best learning experience.'
    },
    {
      title: '3. Attendance and Participation',
      content: 'Regular attendance is expected. If you miss a session, makeup arrangements may be available subject to instructor availability and additional fees may apply.'
    },
    {
      title: '4. Cancellation Policy',
      content: 'Course cancellations must be made at least 24 hours in advance. Refunds are subject to our discretion and may incur administrative fees.'
    },
    {
      title: '5. Intellectual Property',
      content: 'All course materials, including but not limited to presentations, documents, and recordings, are the intellectual property of the instructor and may not be shared or distributed without permission.'
    },
    {
      title: '6. Privacy and Data Protection',
      content: 'Your personal information will be used solely for course administration and communication. We comply with applicable data protection laws in Algeria.'
    },
    {
      title: '7. Limitation of Liability',
      content: 'We strive to provide quality education but cannot guarantee specific outcomes. Our liability is limited to the fees paid for the course.'
    },
    {
      title: '8. Governing Law',
      content: 'These terms are governed by the laws of Algeria. Any disputes will be resolved through the appropriate legal channels in Algeria.'
    }
  ];
  
  terms.forEach(term => {
    if (yPos > 250) {
      doc.addPage();
      yPos = 30;
    }
    
    doc.setFont('helvetica', 'bold');
    doc.text(term.title, 20, yPos);
    yPos += 8;
    
    doc.setFont('helvetica', 'normal');
    const splitContent = doc.splitTextToSize(term.content, pageWidth - 40);
    doc.text(splitContent, 20, yPos);
    yPos += splitContent.length * 5 + 10;
  });
  
  // Footer
  doc.setFontSize(8);
  doc.setTextColor(128, 128, 128);
  doc.text(`Generated on ${new Date().toLocaleDateString()}`, 20, doc.internal.pageSize.height - 10);
  
  doc.save('B_DEV_Terms_and_Conditions.pdf');
};
