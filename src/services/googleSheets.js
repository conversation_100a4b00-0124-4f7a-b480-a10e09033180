import axios from 'axios';

// Google Sheets API configuration
const GOOGLE_SHEETS_CONFIG = {
  // You'll need to replace these with your actual Google Sheets configuration
  SHEET_ID: import.meta.env.VITE_GOOGLE_SHEET_ID,
  API_KEY: import.meta.env.VITE_GOOGLE_SHEETS_API_KEY,
  RANGE: 'Sheet1!A:K', // Adjust range based on your sheet structure
};

// Alternative: Google Apps Script Web App URL (Recommended approach)
const GOOGLE_APPS_SCRIPT_URL = import.meta.env.VITE_GOOGLE_APPS_SCRIPT_URL;

/**
 * Submit form data to Google Sheets using Google Apps Script Web App
 * This is the recommended approach as it's simpler and more secure
 */
export const submitToGoogleSheets = async (formData) => {
  try {
    if (!GOOGLE_APPS_SCRIPT_URL) {
      throw new Error('Google Apps Script URL not configured');
    }

    const dataToSubmit = {
      timestamp: new Date().toISOString(),
      firstName: formData.firstName,
      lastName: formData.lastName,
      wilaya: formData.wilaya,
      country: formData.country,
      age: formData.age,
      universityGrade: formData.universityGrade,
      profession: formData.profession,
      university: formData.university || '',
      jobTitle: formData.jobTitle || '',
      otherProfession: formData.otherProfession || '',
      fieldOfStudies: formData.fieldOfStudies,
      priceProposition: formData.priceProposition,
      motivation: formData.motivation,
      agreeToTerms: formData.agreeToTerms ? 'Yes' : 'No'
    };

    // Try multiple methods to handle CORS issues
    try {
      // Method 1: Standard fetch
      const response = await fetch(GOOGLE_APPS_SCRIPT_URL, {
        method: 'POST',
        mode: 'no-cors', // This bypasses CORS but limits response access
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSubmit)
      });

      // With no-cors mode, we can't read the response, so we assume success
      return { success: true, data: { message: 'Data submitted successfully' } };

    } catch (fetchError) {
      // Method 2: Form submission fallback
      return await submitViaForm(dataToSubmit);
    }
  } catch (error) {
    console.error('Error submitting to Google Sheets:', error);
    throw error;
  }
};

// Fallback method using form submission
const submitViaForm = async (data) => {
  return new Promise((resolve, reject) => {
    try {
      // Create a hidden form
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = GOOGLE_APPS_SCRIPT_URL;
      form.target = '_blank'; // Open in new tab to avoid navigation
      form.style.display = 'none';

      // Add data as form fields
      Object.keys(data).forEach(key => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = data[key];
        form.appendChild(input);
      });

      // Submit form
      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);

      // Assume success since we can't get response with form submission
      setTimeout(() => {
        resolve({ success: true, data: { message: 'Data submitted via form' } });
      }, 1000);

    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Alternative: Export data as CSV for manual upload to Excel/Google Sheets
 */
export const exportAsCSV = (formData) => {
  const csvData = [
    ['Timestamp', 'First Name', 'Last Name', 'Wilaya', 'Country', 'Age', 'University Grade', 'Profession', 'University', 'Job Title', 'Other Profession', 'Field of Studies', 'Price Proposition (DZD)', 'Motivation', 'Agreed to Terms'],
    [
      new Date().toISOString(),
      formData.firstName,
      formData.lastName,
      formData.wilaya,
      formData.country,
      formData.age,
      formData.universityGrade,
      formData.profession,
      formData.university || '',
      formData.jobTitle || '',
      formData.otherProfession || '',
      formData.fieldOfStudies,
      formData.priceProposition,
      formData.motivation,
      formData.agreeToTerms ? 'Yes' : 'No'
    ]
  ];

  const csvContent = csvData.map(row => 
    row.map(field => `"${field}"`).join(',')
  ).join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `course_registration_${Date.now()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

/**
 * Store data in localStorage as backup
 */
export const storeLocally = (formData) => {
  try {
    const existingData = JSON.parse(localStorage.getItem('courseRegistrations') || '[]');
    const newEntry = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      ...formData
    };
    
    existingData.push(newEntry);
    localStorage.setItem('courseRegistrations', JSON.stringify(existingData));
    
    return { success: true, id: newEntry.id };
  } catch (error) {
    console.error('Error storing data locally:', error);
    throw error;
  }
};

/**
 * Get all locally stored registrations
 */
export const getLocalRegistrations = () => {
  try {
    return JSON.parse(localStorage.getItem('courseRegistrations') || '[]');
  } catch (error) {
    console.error('Error retrieving local data:', error);
    return [];
  }
};

/**
 * Export all local registrations as CSV
 */
export const exportAllLocalRegistrations = () => {
  const registrations = getLocalRegistrations();
  
  if (registrations.length === 0) {
    alert('No registrations found to export.');
    return;
  }

  const csvData = [
    ['ID', 'Timestamp', 'First Name', 'Last Name', 'Wilaya', 'Country', 'Age', 'University Grade', 'Profession', 'University', 'Job Title', 'Other Profession', 'Field of Studies', 'Price Proposition (DZD)', 'Motivation', 'Agreed to Terms'],
    ...registrations.map(reg => [
      reg.id,
      reg.timestamp,
      reg.firstName,
      reg.lastName,
      reg.wilaya,
      reg.country,
      reg.age,
      reg.universityGrade,
      reg.profession,
      reg.university || '',
      reg.jobTitle || '',
      reg.otherProfession || '',
      reg.fieldOfStudies,
      reg.priceProposition,
      reg.motivation,
      reg.agreeToTerms ? 'Yes' : 'No'
    ])
  ];

  const csvContent = csvData.map(row => 
    row.map(field => `"${field}"`).join(',')
  ).join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `all_course_registrations_${Date.now()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};
